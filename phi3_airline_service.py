"""
Phi-3 Mini Airline Customer Service
Privacy-first, on-premises customer service AI for airlines
MIT License - Safe for commercial use
"""

import torch
from transformers import <PERSON>Tokenizer, AutoModelForCausalLM, BitsAndBytesConfig
import asyncio
import json
import logging
import time
from typing import Dict, List, Optional
from dataclasses import dataclass
import sqlite3
from datetime import datetime
import threading
from queue import Queue
import re
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/phi3_airline.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class AirlineIncident:
    """Airline customer service incident"""
    text: str
    incident_type: str  # flight_delay, baggage, booking, refund, complaint
    sentiment: str      # positive, negative, neutral
    urgency: str        # low, medium, high, urgent
    customer_tier: str  # regular, frequent, vip
    flight_number: Optional[str] = None
    route: Optional[str] = None
    booking_ref: Optional[str] = None

class Phi3AirlineService:
    """Phi-3 Mini service optimized for airline customer service"""
    
    def __init__(self, config: Dict = None):
        self.config = config or {
            "model_name": os.getenv("PHI3_MODEL", "microsoft/Phi-3-mini-4k-instruct"),
            "use_quantization": os.getenv("PHI3_USE_QUANTIZATION", "true").lower() == "true",
            "max_new_tokens": int(os.getenv("MAX_RESPONSE_LENGTH", "150")),
            "temperature": float(os.getenv("RESPONSE_TEMPERATURE", "0.7")),
            "do_sample": True,
            "cache_dir": os.getenv("PHI3_CACHE_DIR", "./models"),
            "device": os.getenv("PHI3_DEVICE", "auto")
        }
        
        self.model = None
        self.tokenizer = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.is_initialized = False
        self.response_cache = {}
        self.airline_knowledge = self._load_airline_knowledge()
        
        logger.info(f"🎯 Initializing Phi-3 Mini for airline customer service")
        logger.info(f"📱 Device: {self.device}")
        logger.info(f"💾 Cache directory: {self.config['cache_dir']}")
    
    def _load_airline_knowledge(self) -> Dict:
        """Load airline-specific knowledge base"""
        return {
            "compensation_rules": {
                "flight_delay": {
                    "0-3_hours": "Meal vouchers up to $30, rebooking assistance",
                    "3-6_hours": "Meal vouchers up to $75, hotel if overnight",
                    "6+_hours": "Compensation $200-400, meals, hotel accommodation",
                    "cancellation": "Full refund or rebooking, compensation $200-675"
                },
                "baggage": {
                    "delayed": "$150/day for essentials up to 21 days",
                    "damaged": "Repair cost or depreciated value up to $3,500",
                    "lost": "Compensation up to $3,500 based on contents"
                }
            },
            "escalation_triggers": [
                "legal action", "lawsuit", "discrimination", "safety concern",
                "medical emergency", "unaccompanied minor", "disability",
                "service animal", "wheelchair", "pregnant", "infant"
            ],
            "airline_procedures": {
                "baggage_trace": "WorldTracer system for international tracking",
                "rebooking": "Same-day rebooking on next available flight",
                "compensation": "Processed within 7-14 business days",
                "refunds": "Original payment method, 7-21 business days"
            }
        }
    
    async def initialize(self):
        """Initialize Phi-3 Mini model"""
        try:
            logger.info("🔄 Loading Phi-3 Mini model...")
            start_time = time.time()
            
            # Configure quantization for memory efficiency
            if self.config.get("use_quantization", True) and self.device == "cuda":
                quantization_config = BitsAndBytesConfig(
                    load_in_4bit=True,
                    bnb_4bit_compute_dtype=torch.float16,
                    bnb_4bit_use_double_quant=True,
                    bnb_4bit_quant_type="nf4"
                )
                logger.info("⚡ Using 4-bit quantization for memory efficiency")
            else:
                quantization_config = None
                logger.info("💾 Using full precision (no quantization)")
            
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.config["model_name"],
                cache_dir=self.config["cache_dir"],
                trust_remote_code=True
            )
            
            # Add pad token if missing
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
                logger.info("🔧 Added pad token to tokenizer")
            
            # Load model
            self.model = AutoModelForCausalLM.from_pretrained(
                self.config["model_name"],
                cache_dir=self.config["cache_dir"],
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                device_map="auto" if self.device == "cuda" else None,
                trust_remote_code=True,
                quantization_config=quantization_config
            )
            
            # Move to device if CPU
            if self.device == "cpu":
                self.model = self.model.to(self.device)
            
            load_time = time.time() - start_time
            self.is_initialized = True
            
            logger.info(f"✅ Phi-3 Mini loaded successfully in {load_time:.2f} seconds")
            logger.info(f"📊 Model parameters: ~3.8B")
            logger.info(f"💾 Memory usage: ~{self._get_memory_usage():.1f}GB")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Phi-3 Mini: {e}")
            raise
    
    def _get_memory_usage(self) -> float:
        """Get approximate memory usage in GB"""
        if self.device == "cuda" and torch.cuda.is_available():
            return torch.cuda.memory_allocated() / 1024**3
        else:
            # Estimate for CPU (Phi-3 Mini is ~3.8B params * 2 bytes = ~7.6GB)
            return 7.6 if self.config.get("use_quantization") else 15.2
    
    async def generate_response(self, incident: AirlineIncident) -> Dict:
        """Generate airline customer service response using Phi-3 Mini"""
        
        if not self.is_initialized:
            raise RuntimeError("Model not initialized. Call initialize() first.")
        
        try:
            start_time = time.time()
            
            # Check cache first
            cache_key = self._get_cache_key(incident)
            if cache_key in self.response_cache:
                cached_response = self.response_cache[cache_key]
                cached_response["from_cache"] = True
                return cached_response
            
            # Create airline-specific prompt
            prompt = self._create_airline_prompt(incident)
            
            # Tokenize input
            inputs = self.tokenizer(
                prompt,
                return_tensors="pt",
                truncation=True,
                max_length=3072,  # Leave room for response
                padding=True
            ).to(self.device)
            
            # Generate response
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=self.config.get("max_new_tokens", 150),
                    temperature=self.config.get("temperature", 0.7),
                    do_sample=self.config.get("do_sample", True),
                    top_p=0.9,
                    repetition_penalty=1.1,
                    pad_token_id=self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id
                )
            
            # Decode response
            generated_text = self.tokenizer.decode(
                outputs[0][inputs['input_ids'].shape[1]:],
                skip_special_tokens=True
            )
            
            # Clean and post-process response
            response_text = self._post_process_response(generated_text, incident)
            
            # Calculate metrics
            generation_time = int((time.time() - start_time) * 1000)
            confidence_score = self._calculate_confidence(response_text, incident)
            requires_escalation = self._check_escalation_needed(incident)
            
            # Build response object
            response = {
                "response_text": response_text,
                "confidence_score": confidence_score,
                "model_used": "microsoft/Phi-3-mini-4k-instruct",
                "generation_time_ms": generation_time,
                "requires_escalation": requires_escalation,
                "incident_classification": {
                    "type": incident.incident_type,
                    "urgency": incident.urgency,
                    "sentiment": incident.sentiment,
                    "customer_tier": incident.customer_tier
                },
                "suggested_actions": self._get_suggested_actions(incident),
                "policy_references": self._get_policy_references(incident),
                "privacy_status": "✅ Processed locally with Phi-3 Mini",
                "from_cache": False
            }
            
            # Cache response
            self.response_cache[cache_key] = response
            
            logger.info(f"✅ Generated response in {generation_time}ms (confidence: {confidence_score:.2f})")
            return response
            
        except Exception as e:
            logger.error(f"❌ Error generating response: {e}")
            return self._generate_fallback_response(incident)
    
    def _create_airline_prompt(self, incident: AirlineIncident) -> str:
        """Create airline-specific prompt for Phi-3 Mini"""
        
        # System context optimized for Phi-3 Mini with multi-language support
        system_context = """You are a professional airline customer service representative. Respond with empathy and provide specific next steps.

GUIDELINES:
- Be professional and empathetic
- Acknowledge the customer's concern
- Provide clear next steps
- Ask for booking reference via DM
- Keep responses under 280 characters
- Use appropriate compensation policies
- Escalate serious issues
- IMPORTANT: Respond in the same language as the customer's message
- Recognize airline terms in any language (flight numbers, delays, cancellations)

MULTI-LANGUAGE RECOGNITION:
- Tamil: விமானம் (flight), தாமதம் (delay), ரத்து (cancel), பிளாடினம் (platinum)
- Hindi: विमान (flight), देरी (delay), रद्द (cancel), प्लेटिनम (platinum)
- Spanish: vuelo (flight), retraso (delay), cancelado (cancel), platino (platinum)
- French: vol (flight), retard (delay), annulé (cancel), platine (platinum)

TONE: Professional, empathetic, solution-focused, culturally appropriate"""
        
        # Build context
        context_parts = [
            f"INCIDENT: {incident.incident_type}",
            f"SENTIMENT: {incident.sentiment}",
            f"URGENCY: {incident.urgency}",
            f"CUSTOMER: {incident.customer_tier}"
        ]
        
        if incident.flight_number:
            context_parts.append(f"FLIGHT: {incident.flight_number}")
        
        # Add relevant policies
        if incident.incident_type in self.airline_knowledge["compensation_rules"]:
            policies = list(self.airline_knowledge["compensation_rules"][incident.incident_type].keys())
            context_parts.append(f"POLICIES: {', '.join(policies[:2])}")
        
        # Check for escalation
        escalation_needed = any(trigger in incident.text.lower() 
                              for trigger in self.airline_knowledge["escalation_triggers"])
        if escalation_needed:
            context_parts.append("⚠️ ESCALATION REQUIRED")
        
        context = "\n".join(context_parts)
        
        # Create full prompt optimized for Phi-3
        prompt = f"""<|system|>
{system_context}

{context}

<|user|>
Customer message: "{incident.text}"

<|assistant|>
"""
        
        return prompt
    
    def _post_process_response(self, generated_text: str, incident: AirlineIncident) -> str:
        """Clean and optimize generated response"""
        
        # Clean up response
        response = generated_text.strip()
        
        # Remove common artifacts
        response = re.sub(r'^(Response:|Assistant:|AI:|Bot:)', '', response, flags=re.IGNORECASE).strip()
        response = re.sub(r'^["\']|["\']$', '', response).strip()
        
        # Take first paragraph if multiple
        response = response.split('\n')[0].strip()
        
        # Ensure proper greeting
        if not response.startswith(("Hi", "Hello", "Thank you", "We", "I")):
            if incident.customer_tier == "vip":
                response = f"Hi! As a valued elite member, {response.lower()}"
            elif incident.customer_tier == "frequent":
                response = f"Hi! As a loyal frequent flyer, {response.lower()}"
            else:
                response = f"Hi! {response}"
        
        # Ensure DM request
        if "DM" not in response and "direct message" not in response.lower():
            if "booking" not in response.lower() and "reference" not in response.lower():
                response += " Please DM us your booking reference for assistance."
        
        # Add appropriate emoji
        emoji_map = {
            "flight_delay": "✈️",
            "baggage": "🧳",
            "booking": "🎫",
            "refund": "💳",
            "complaint": "🙏" if incident.sentiment == "negative" else "✈️"
        }
        
        emoji = emoji_map.get(incident.incident_type, "✈️")
        if emoji not in response:
            response += f" {emoji}"
        
        # Ensure length limit
        if len(response) > 280:
            response = response[:277] + "..."
        
        return response
    
    def _calculate_confidence(self, response: str, incident: AirlineIncident) -> float:
        """Calculate confidence score"""
        
        confidence = 0.8  # Base confidence for Phi-3 Mini
        
        # Check for airline-specific terms
        airline_terms = ["booking", "flight", "reference", "DM", "assist", "compensation"]
        if any(term in response.lower() for term in airline_terms):
            confidence += 0.1
        
        # Check for appropriate tone
        polite_terms = ["sorry", "apologize", "understand", "help", "assist"]
        if any(term in response.lower() for term in polite_terms):
            confidence += 0.05
        
        # Check response length
        if 50 <= len(response) <= 280:
            confidence += 0.05
        
        return min(0.95, max(0.6, confidence))
    
    def _check_escalation_needed(self, incident: AirlineIncident) -> bool:
        """Check if escalation is needed"""
        
        # Check escalation triggers
        if any(trigger in incident.text.lower() 
               for trigger in self.airline_knowledge["escalation_triggers"]):
            return True
        
        # Urgent incidents
        if incident.urgency == "urgent":
            return True
        
        # Negative complaints
        if incident.incident_type == "complaint" and incident.sentiment == "negative":
            return True
        
        return False
    
    def _get_suggested_actions(self, incident: AirlineIncident) -> List[str]:
        """Get suggested actions for agents"""
        
        actions = ["Verify customer identity", "Review booking details"]
        
        action_map = {
            "flight_delay": [
                "Check flight status and delay reason",
                "Review rebooking options",
                "Calculate compensation eligibility",
                "Provide meal vouchers if delay > 3 hours"
            ],
            "baggage": [
                "Initiate WorldTracer baggage trace",
                "Provide interim expense allowance",
                "Schedule delivery when found",
                "Process compensation if lost"
            ],
            "booking": [
                "Review booking modification options",
                "Check fare rules for changes",
                "Process booking changes"
            ],
            "refund": [
                "Review fare rules and eligibility",
                "Calculate refund amount",
                "Process refund request"
            ]
        }
        
        actions.extend(action_map.get(incident.incident_type, []))
        
        if incident.customer_tier in ["frequent", "vip"]:
            actions.append("Apply elite member benefits")
        
        return actions
    
    def _get_policy_references(self, incident: AirlineIncident) -> List[str]:
        """Get relevant policy references"""
        
        policy_map = {
            "flight_delay": ["DOT Rule 240", "EU Regulation 261/2004", "Contract of Carriage"],
            "baggage": ["Montreal Convention", "DOT Baggage Rules", "WorldTracer System"],
            "refund": ["DOT 24-Hour Rule", "Airline Refund Policy", "Fare Rules"],
            "booking": ["Contract of Carriage", "Fare Rules", "Change Policies"]
        }
        
        return policy_map.get(incident.incident_type, ["General Terms and Conditions"])
    
    def _get_cache_key(self, incident: AirlineIncident) -> str:
        """Generate cache key"""
        return f"{incident.incident_type}_{incident.sentiment}_{incident.urgency}_{incident.customer_tier}"
    
    def _generate_fallback_response(self, incident: AirlineIncident) -> Dict:
        """Generate fallback response if model fails"""
        
        fallback_templates = {
            "flight_delay": "Hi! We sincerely apologize for the flight delay. Please DM us your booking reference for rebooking assistance and compensation information. ✈️",
            "baggage": "Hi! We're sorry about your baggage issue. Please DM us your claim number and we'll track it immediately. 🧳",
            "booking": "Hi! Thank you for contacting us about your booking. Please DM us your confirmation number for assistance. 🎫",
            "refund": "Hi! We understand you're requesting a refund. Please DM us your booking reference for review. 💳",
            "complaint": "Hi! We sincerely apologize for your experience. Please DM us your booking reference so we can investigate. 🙏",
            "general_complaint": "Hi! We sincerely apologize for any inconvenience. Please DM us your booking reference and details so we can assist you properly. ✈️"
        }
        
        response_text = fallback_templates.get(incident.incident_type, fallback_templates["complaint"])
        
        if incident.customer_tier == "vip":
            response_text = response_text.replace("Hi!", "Hi! As a valued elite member,")
        
        return {
            "response_text": response_text,
            "confidence_score": 0.75,
            "model_used": "fallback_template",
            "generation_time_ms": 10,
            "requires_escalation": incident.urgency in ["urgent", "high"],
            "incident_classification": {
                "type": incident.incident_type,
                "urgency": incident.urgency,
                "sentiment": incident.sentiment,
                "customer_tier": incident.customer_tier
            },
            "suggested_actions": self._get_suggested_actions(incident),
            "policy_references": self._get_policy_references(incident),
            "privacy_status": "✅ Fallback template (local processing)",
            "from_cache": False
        }

# Example usage
async def main():
    """Example usage of Phi-3 Mini airline service"""
    
    print("🚀 Initializing Phi-3 Mini Airline Customer Service")
    print("=" * 60)
    
    # Initialize service
    service = Phi3AirlineService()
    await service.initialize()
    
    # Example incident
    incident = AirlineIncident(
        text="My flight AA123 from LAX to JFK was delayed 4 hours and I missed my connection. This is unacceptable!",
        incident_type="flight_delay",
        sentiment="negative",
        urgency="high",
        customer_tier="frequent",
        flight_number="AA123",
        route="LAX-JFK"
    )
    
    print(f"📝 Customer Message: {incident.text}")
    print(f"📊 Classification: {incident.incident_type} | {incident.sentiment} | {incident.urgency}")
    print()
    
    # Generate response
    response = await service.generate_response(incident)
    
    print(f"🤖 Phi-3 Mini Response:")
    print(f"   {response['response_text']}")
    print()
    print(f"📈 Metrics:")
    print(f"   Confidence: {response['confidence_score']:.2f}")
    print(f"   Generation Time: {response['generation_time_ms']}ms")
    print(f"   Escalation Needed: {response['requires_escalation']}")
    print(f"   Privacy: {response['privacy_status']}")

if __name__ == "__main__":
    asyncio.run(main())
