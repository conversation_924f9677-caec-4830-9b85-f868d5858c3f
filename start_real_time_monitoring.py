#!/usr/bin/env python3
"""
Start Real-Time Monitoring for @KarthikHar25823
This script continuously monitors Twitter and feeds data to your admin UI
"""

import requests
import os
import time
import json
from datetime import datetime, timedelta
from dotenv import load_dotenv
from app.services.twitter_query_builder import build_simple_query, build_api_url

# Load environment variables
load_dotenv()

class RealTimeMonitor:
    def __init__(self):
        self.backend_url = "http://localhost:8001"
        self.twitter_username = "KarthikHar25823"
        self.bearer_token = os.getenv('TWITTER_BEARER_TOKEN')
        self.last_tweet_id = None

    def log_debug(self, level, message):
        """Send debug log to backend"""
        try:
            requests.post(f"{self.backend_url}/api/v1/debug/logs",
                         json={'level': level, 'message': message},
                         timeout=2)
        except:
            pass  # Don't fail if debug logging fails
        
    def get_account_settings(self):
        """Get monitoring settings for the account"""
        try:
            response = requests.get(f"{self.backend_url}/api/v1/social-accounts/{self.twitter_username}/settings")
            print(f"   🔍 Settings API response status: {response.status_code}")
            if response.status_code == 200:
                settings = response.json()
                print(f"   🔍 Raw settings from API: {settings}")
                return settings
            else:
                print(f"   ⚠️ API returned {response.status_code}, using defaults")
                # Return default settings if not found
                default_settings = {
                    'monitor_mentions': True,
                    'monitor_keywords': ['flight delay', 'customer service', 'airline help'],
                    'monitor_hashtags': ['help', 'support'],
                    'realtime_monitoring': True,
                    'auto_generate': True
                }
                print(f"   🔍 Using default settings: {default_settings}")
                return default_settings
        except Exception as e:
            print(f"   ⚠️ Could not load settings: {e}")
            default_settings = {
                'monitor_mentions': True,
                'monitor_keywords': ['flight delay', 'customer service', 'airline help'],
                'monitor_hashtags': ['help', 'support'],
                'realtime_monitoring': True,
                'auto_generate': True
            }
            print(f"   🔍 Using fallback settings: {default_settings}")
            return default_settings

    def get_monitoring_status(self):
        """Get global monitoring status from backend"""
        try:
            response = requests.get(f"{self.backend_url}/api/v1/monitoring/status")
            if response.status_code == 200:
                return response.json()
            else:
                return {'enabled': True}  # Default to enabled
        except Exception as e:
            print(f"   ⚠️ Could not get monitoring status: {e}")
            return {'enabled': True}  # Default to enabled

    def monitor_mentions(self, force_check=False):
        """Monitor Twitter for real mentions"""
        settings = self.get_account_settings()

        if not force_check and not settings.get('realtime_monitoring', True):
            print(f"⏸️ Real-time monitoring disabled for @{self.twitter_username}")
            return 0

        if force_check:
            print(f"🔄 Manual trigger - bypassing realtime_monitoring setting")

        print(f"🔍 Checking for mentions of @{self.twitter_username}...")

        headers = {
            'Authorization': f'Bearer {self.bearer_token}',
            'Content-Type': 'application/json'
        }

        # Build query using centralized query builder
        try:
            print(f"   🔍 Raw settings: {settings}")

            # Use centralized query builder
            query = build_simple_query(settings, self.twitter_username)
            print(f"   🔍 Generated query: {query}")

            # Build complete API URL with proper parameters
            # Use configurable time window from settings (default to 2 hours)
            time_window_hours = settings.get('time_window_hours', 2)

            # Handle "All Time" option (0 = no time limit)
            if time_window_hours == 0:
                start_time = None
                print(f"   ⏰ Time window: All Time (no limit)")
            else:
                start_time = datetime.now() - timedelta(hours=time_window_hours)
                print(f"   ⏰ Time window: {time_window_hours} hours")

            url = build_api_url(
                query=query,
                max_results=10,
                tweet_fields=["created_at", "author_id", "public_metrics"],
                user_fields=["username", "name"],
                expansions=["author_id"],
                start_time=start_time,
                since_id=self.last_tweet_id
            )

            print(f"   🔗 API URL: {url}")

        except Exception as e:
            print(f"   ❌ Error building query: {e}")
            print(f"   🔧 Using fallback query...")
            query = f"@{self.twitter_username}"
            url = f'https://api.twitter.com/2/tweets/search/recent?query={query}&max_results=10'
        
        try:
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'data' in data and data['data']:
                    tweets = data['data']
                    users = {user['id']: user for user in data.get('includes', {}).get('users', [])}
                    
                    print(f"   ✅ Found {len(tweets)} new mentions!")
                    
                    new_mentions = 0
                    for tweet in tweets:
                        author_info = users.get(tweet['author_id'], {})
                        
                        # Store in backend
                        if self.store_real_mention(tweet, author_info):
                            new_mentions += 1
                            
                        # Update last tweet ID
                        if not self.last_tweet_id or int(tweet['id']) > int(self.last_tweet_id):
                            self.last_tweet_id = tweet['id']
                    
                    if new_mentions > 0:
                        print(f"   📝 Processed {new_mentions} new mentions")
                        return new_mentions
                    else:
                        print(f"   ⚪ All mentions already processed")
                        return 0
                else:
                    print("   ⚪ No new mentions found")
                    return 0
                    
            elif response.status_code == 429:
                # Get rate limit reset time from headers
                reset_time = response.headers.get('x-rate-limit-reset')
                if reset_time:
                    reset_timestamp = int(reset_time)
                    current_timestamp = int(time.time())
                    wait_time = max(0, reset_timestamp - current_timestamp)
                    rate_limit_msg = f"Rate limited - API resets in {wait_time} seconds"
                    print(f"   ⚠️ {rate_limit_msg}")
                    self.log_debug('warning', rate_limit_msg)

                    if wait_time > 0 and wait_time < 900:  # Less than 15 minutes
                        wait_msg = f"Waiting {wait_time} seconds for rate limit reset..."
                        print(f"   ⏳ {wait_msg}")
                        self.log_debug('info', wait_msg)

                        time.sleep(wait_time + 10)  # Add 10 second buffer

                        retry_msg = "Rate limit reset, retrying..."
                        print(f"   🔄 {retry_msg}")
                        self.log_debug('info', retry_msg)
                        # Retry the request once
                        try:
                            response = requests.get(url, headers=headers, timeout=10)
                            if response.status_code == 200:
                                data = response.json()
                                if 'data' in data and data['data']:
                                    tweets = data['data']
                                    users = {user['id']: user for user in data.get('includes', {}).get('users', [])}
                                    print(f"   ✅ Found {len(tweets)} new mentions after rate limit reset!")

                                    # Process tweets
                                    new_mentions = 0
                                    for tweet in tweets:
                                        author_info = users.get(tweet['author_id'], {})

                                        # Store in backend
                                        if self.store_real_mention(tweet, author_info):
                                            new_mentions += 1

                                        # Update last tweet ID
                                        if not self.last_tweet_id or int(tweet['id']) > int(self.last_tweet_id):
                                            self.last_tweet_id = tweet['id']

                                    return new_mentions
                                else:
                                    print("   ⚪ No new mentions found after retry")
                                    return 0
                            else:
                                print(f"   ❌ Still rate limited after waiting")
                                return None
                        except Exception as e:
                            print(f"   ❌ Error during retry: {e}")
                            return None
                    else:
                        print(f"   ⚠️ Rate limit reset time too long ({wait_time}s), will retry in next cycle")
                        return None
                else:
                    print("   ⚠️ Rate limited - will retry in next cycle")
                    return None
            else:
                print(f"   ❌ Error {response.status_code}: {response.text}")
                return None
                
        except Exception as e:
            print(f"   ❌ Failed to monitor mentions: {e}")
            return None
    
    def store_real_mention(self, tweet_data, author_data):
        """Store a real mention in the backend"""
        try:
            # Check if already exists
            existing_check = requests.get(
                f"{self.backend_url}/api/v1/posts/check/{tweet_data['id']}",
                timeout=5
            )
            
            if existing_check.status_code == 200:
                print(f"   ⚪ Tweet {tweet_data['id']} already processed")
                return False
            
            # Store new mention
            mention_data = {
                'platform': 'twitter',
                'external_id': tweet_data['id'],
                'content': tweet_data['text'],
                'author_username': author_data.get('username', 'unknown'),
                'author_display_name': author_data.get('name', 'Unknown'),
                'post_url': f"https://twitter.com/{author_data.get('username', 'unknown')}/status/{tweet_data['id']}",
                'posted_at': tweet_data['created_at'],
                'requires_response': True
            }
            
            response = requests.post(
                f"{self.backend_url}/api/v1/posts/",
                json=mention_data,
                timeout=10
            )
            
            if response.status_code == 201:
                print(f"   ✅ Stored mention from @{author_data.get('username', 'unknown')}")
                print(f"      Content: {tweet_data['text'][:60]}...")
                return True
            else:
                print(f"   ❌ Failed to store mention: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ Error storing mention: {e}")
            return False
    
    def get_backend_stats(self):
        """Get current backend statistics"""
        try:
            response = requests.get(f"{self.backend_url}/api/v1/dashboard/stats", timeout=5)
            if response.status_code == 200:
                return response.json()
            return None
        except:
            return None
    
    def run_monitoring_cycle(self, manual_trigger=False):
        """Run one monitoring cycle"""
        cycle_type = "Manual Trigger" if manual_trigger else "Monitoring Cycle"
        print(f"\n🔄 {cycle_type} - {datetime.now().strftime('%H:%M:%S')}")
        print("=" * 60)

        # For manual triggers, skip the global monitoring check
        if not manual_trigger:
            # Check if monitoring is globally enabled
            monitoring_status = self.get_monitoring_status()
            if not monitoring_status.get('enabled', True):
                print("⏸️ Monitoring is currently disabled via admin UI")
                print("   Enable monitoring in the admin UI to resume")
                return {
                    'new_mentions': 0,
                    'stats': None,
                    'disabled': True,
                    'timestamp': datetime.now().isoformat()
                }

        # Monitor for new mentions (force check for manual triggers)
        new_mentions = self.monitor_mentions(force_check=manual_trigger)

        # Get current stats
        stats = self.get_backend_stats()

        if stats:
            print(f"\n📊 Current Stats:")
            print(f"   📱 Active Accounts: {stats.get('active_accounts', 0)}")
            print(f"   📝 Posts Detected: {stats.get('posts_detected', 0)}")
            print(f"   ⏳ Pending Responses: {stats.get('pending_responses', 0)}")
            print(f"   ✅ Posted Responses: {stats.get('posted_responses', 0)}")

        print(f"\n🎛️ Admin UI: http://localhost:3000")
        print(f"   📋 Check 'Response Management' for pending approvals")

        return {
            'new_mentions': new_mentions,
            'stats': stats,
            'disabled': False,
            'timestamp': datetime.now().isoformat()
        }


def main():
    """Start real-time monitoring"""
    print("🚀 STARTING REAL-TIME MONITORING")
    print("🤖 @KarthikHar25823 AI Social Media Handler")
    print("=" * 70)
    print(f"Monitoring started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Initialize monitor
    monitor = RealTimeMonitor()
    
    print(f"\n🎯 Configuration:")
    print(f"   Twitter Account: @{monitor.twitter_username}")
    print(f"   Backend API: {monitor.backend_url}")
    print(f"   Admin UI: http://localhost:3000")
    print(f"   Monitoring Interval: 60 seconds")
    
    print(f"\n🔄 Starting continuous monitoring...")
    print(f"   Press Ctrl+C to stop")
    
    cycle_count = 0
    
    try:
        while True:
            cycle_count += 1
            
            print(f"\n{'='*70}")
            print(f"📡 CYCLE {cycle_count}")
            
            result = monitor.run_monitoring_cycle()
            
            if result['new_mentions'] and result['new_mentions'] > 0:
                print(f"\n🎉 NEW ACTIVITY DETECTED!")
                print(f"   📝 {result['new_mentions']} new mentions processed")
                print(f"   🎛️ Check your admin UI for pending responses")
                print(f"   🔗 http://localhost:3000")
            
            print(f"\n⏳ Waiting 60 seconds before next check...")
            time.sleep(60)  # Check every minute
            
    except KeyboardInterrupt:
        print(f"\n⏹️ Monitoring stopped by user")
        print(f"📊 Total cycles completed: {cycle_count}")
        
        # Final stats
        final_stats = monitor.get_backend_stats()
        if final_stats:
            print(f"\n📈 Final Statistics:")
            print(f"   📝 Total Posts Detected: {final_stats.get('posts_detected', 0)}")
            print(f"   ⏳ Pending Responses: {final_stats.get('pending_responses', 0)}")
            print(f"   ✅ Posted Responses: {final_stats.get('posted_responses', 0)}")
        
        print(f"\n🎛️ Your admin UI is still available at: http://localhost:3000")
        print(f"🔧 Backend API is still running at: {monitor.backend_url}")
        
    except Exception as e:
        print(f"\n❌ Error in monitoring: {e}")


if __name__ == "__main__":
    main()
