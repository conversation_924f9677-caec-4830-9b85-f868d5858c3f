#!/usr/bin/env python3
"""
Fetch a specific tweet with media URLs using the existing Twitter client
"""

import sqlite3
import json
import tweepy
from datetime import datetime

def fetch_tweet_with_media(tweet_id: str):
    """Fetch a specific tweet and extract its media URLs"""

    # Import the backend to get Twitter credentials
    import sys
    sys.path.append('.')
    from real_time_backend import RealTimeBackend

    backend = RealTimeBackend()

    # Get Twitter credentials
    credentials = backend.get_secure_twitter_credentials()
    if not credentials:
        print("❌ No Twitter credentials found")
        return None

    try:
        # Create Twitter client with media support
        client = tweepy.Client(
            bearer_token=credentials.get("bearer_token"),
            consumer_key=credentials.get("api_key"),
            consumer_secret=credentials.get("api_secret"),
            access_token=credentials.get("access_token"),
            access_token_secret=credentials.get("access_token_secret"),
            wait_on_rate_limit=True
        )

        print(f"✅ Twitter client created")

        # Get tweet details with media expansions
        tweet = client.get_tweet(
            tweet_id,
            tweet_fields=["created_at", "author_id", "public_metrics", "attachments"],
            user_fields=["username", "name"],
            expansions=["author_id", "attachments.media_keys"],
            media_fields=["type", "url", "preview_image_url", "alt_text"]
        )

        if not tweet.data:
            print(f"❌ Could not fetch tweet {tweet_id}")
            return None

        print(f"✅ Fetched tweet details:")
        print(f"   ID: {tweet.data.id}")
        print(f"   Text: {tweet.data.text[:100]}...")

        # Get author info
        author = None
        if tweet.includes and "users" in tweet.includes:
            author = tweet.includes["users"][0]
            print(f"   Author: @{author.username}")

        # Extract media URLs
        media_urls = []
        if tweet.includes and "media" in tweet.includes:
            for media_obj in tweet.includes["media"]:
                if media_obj.type == 'photo' and hasattr(media_obj, 'url'):
                    media_urls.append(media_obj.url)
                    print(f"   📷 Photo: {media_obj.url}")
                elif media_obj.type == 'video' and hasattr(media_obj, 'preview_image_url'):
                    media_urls.append(media_obj.preview_image_url)
                    print(f"   🎥 Video preview: {media_obj.preview_image_url}")

        print(f"   Media URLs: {len(media_urls)} found")

        # Prepare data for storage
        tweet_data = {
            'id': str(tweet.data.id),
            'text': tweet.data.text,
            'created_at': tweet.data.created_at.isoformat() + 'Z',
            'media_urls': media_urls
        }

        author_data = {
            'username': author.username if author else 'unknown',
            'name': author.name if author else 'Unknown'
        }

        return tweet_data, author_data

    except Exception as e:
        print(f"❌ Error fetching tweet: {e}")
        import traceback
        traceback.print_exc()
        return None

def store_tweet_in_db(tweet_data, author_data):
    """Store the fetched tweet in the database"""
    if not tweet_data:
        return None

    # Import the backend to use its store_mention function
    import sys
    sys.path.append('.')
    from real_time_backend import RealTimeBackend

    backend = RealTimeBackend()

    print(f"\n🔄 Storing tweet in database...")
    post_id = backend.store_mention(tweet_data, author_data)

    if post_id:
        print(f"✅ Tweet stored successfully with post_id: {post_id}")
        return post_id
    else:
        print(f"❌ Failed to store tweet")
        return None

def main():
    """Main function"""
    tweet_id = "1930961136805150764"

    print(f"🔍 Fetching tweet {tweet_id} with media URLs...")

    # Fetch the tweet
    result = fetch_tweet_with_media(tweet_id)

    if result:
        tweet_data, author_data = result

        # Store in database
        post_id = store_tweet_in_db(tweet_data, author_data)

        if post_id:
            print(f"\n🎉 Successfully fetched and stored tweet with media!")
            print(f"   Post ID: {post_id}")
            print(f"   Media URLs: {len(tweet_data.get('media_urls', []))} found")
        else:
            print(f"\n❌ Failed to store tweet in database")
    else:
        print(f"\n❌ Failed to fetch tweet")

if __name__ == "__main__":
    main()
