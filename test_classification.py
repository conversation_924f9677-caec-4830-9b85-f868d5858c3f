#!/usr/bin/env python3
"""
Test script to verify the new intelligent classification system
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from real_time_backend import RealTimeBackend

def test_classification():
    """Test the new classification system with various airline issues"""
    
    backend = RealTimeBackend()
    
    # Test cases covering various airline issues
    test_cases = [
        # Tamil flight delay (your example)
        {
            "text": "@KarthikHar25823 எனது விமானம் AA123 4 மணி நேரம் தாமதமாகிவிட்டது! எனக்கு பிளாடினம் உறுப்பினராக நிகர்மம் வேண்டும்!",
            "expected_category": "flight_delay",
            "description": "Tamil flight delay with platinum status"
        },
        
        # Broken baggage (your example)
        {
            "text": "@KarthikHar25823 My suitcase arrived completely damaged! The wheels are broken and there's a huge tear. Flight AA456 from LAX to JFK.",
            "expected_category": "baggage", 
            "description": "Broken baggage without 'baggage' keyword"
        },
        
        # Aerobridge issue (your example)
        {
            "text": "@KarthikHar25823 The jet bridge was broken at gate 15 and we had to walk on the tarmac in the rain! Unacceptable service!",
            "expected_category": "airport_experience",
            "description": "Aerobridge/jet bridge issue"
        },
        
        # Flight food issue (your example)
        {
            "text": "@KarthikHar25823 The meal served on flight DL789 was spoiled and made me sick. This is a health hazard!",
            "expected_category": "service_quality",
            "description": "Flight food quality issue"
        },
        
        # Hindi complaint
        {
            "text": "@KarthikHar25823 मेरी उड़ान AI101 रद्द हो गई है! मुझे तुरंत दूसरी फ्लाइट चाहिए।",
            "expected_category": "flight_delay",
            "description": "Hindi flight cancellation"
        },
        
        # Spanish baggage
        {
            "text": "@KarthikHar25823 Mi equipaje se perdió en el vuelo IB456. Necesito ayuda urgente!",
            "expected_category": "baggage",
            "description": "Spanish lost baggage"
        },
        
        # Seat comfort issue
        {
            "text": "@KarthikHar25823 The seat on my flight was broken and wouldn't recline. Very uncomfortable 8-hour flight!",
            "expected_category": "service_quality",
            "description": "Seat comfort issue"
        },
        
        # Wheelchair assistance
        {
            "text": "@KarthikHar25823 I requested wheelchair assistance but no one came to help me at the gate. I'm a disabled passenger!",
            "expected_category": "accessibility",
            "description": "Wheelchair assistance issue"
        },
        
        # Non-airline complaint (should be general_complaint)
        {
            "text": "@KarthikHar25823 Your website is down and I can't access my account for online banking.",
            "expected_category": "general_complaint",
            "description": "Non-airline related complaint"
        }
    ]
    
    print("🧪 Testing Intelligent Classification System")
    print("=" * 60)
    
    correct_classifications = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['description']}")
        print(f"   Text: {test_case['text'][:80]}...")
        
        # Test classification
        try:
            classified_type = backend._classify_incident_type(test_case['text'])
            expected = test_case['expected_category']
            
            if classified_type == expected:
                print(f"   ✅ CORRECT: {classified_type}")
                correct_classifications += 1
            else:
                print(f"   ❌ WRONG: Got '{classified_type}', Expected '{expected}'")
                
        except Exception as e:
            print(f"   💥 ERROR: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 RESULTS: {correct_classifications}/{total_tests} correct ({correct_classifications/total_tests*100:.1f}%)")
    
    if correct_classifications >= total_tests * 0.8:  # 80% threshold
        print("🎉 CLASSIFICATION SYSTEM WORKING WELL!")
    else:
        print("⚠️  CLASSIFICATION SYSTEM NEEDS IMPROVEMENT")
    
    return correct_classifications, total_tests

if __name__ == "__main__":
    test_classification()
