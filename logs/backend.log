/Users/<USER>/Documents/augment-projects/Agentic Social Handler/airline_phi3_env/lib/python3.9/site-packages/urllib3/__init__.py:35: NotOpenSSLWarning: urllib3 v2 only supports OpenSSL 1.1.1+, currently the 'ssl' module is compiled with 'LibreSSL 2.8.3'. See: https://github.com/urllib3/urllib3/issues/3020
  warnings.warn(
2025-06-09 18:59:57,965 - phi3_airline_service - INFO - 🎯 Initializing Phi-3 Mini for airline customer service
2025-06-09 18:59:57,987 - phi3_airline_service - INFO - 📱 Device: cpu
2025-06-09 18:59:57,987 - phi3_airline_service - INFO - 💾 Cache directory: ./models
✅ Phi-3 Mini service available
✅ User management available
✅ Secure credential management available
✅ Twitter client ready (using secure credentials)
✅ Phi-3 Mini service ready (will initialize on first use)
🚀 Starting Agentic ORM Backend...
✅ Database initialized
✅ Twitter client ready
✅ User management enabled
👤 Default admin: username=admin, password=admin123
📱 Admin UI will show real data
🌐 Backend running on http://localhost:8001
📚 API Endpoints:
   - GET  /health
   - GET  /api/v1/social-accounts/
   - GET  /api/v1/responses/
   - POST /api/v1/responses/{id}/approve
   - POST /api/v1/responses/{id}/regenerate
   - POST /api/v1/auth/login
   - POST /api/v1/auth/logout
   - GET  /api/v1/auth/me
   - GET  /api/v1/users/
   - POST /api/v1/users/
   - PUT  /api/v1/users/{id}/social-accounts
   - DELETE /api/v1/users/{id}
   - GET  /api/v1/credentials/
   - POST /api/v1/credentials/
   - DELETE /api/v1/credentials/{id}

 * Serving Flask app 'real_time_backend'
 * Debug mode: on
2025-06-09 18:59:58,037 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8001
 * Running on http://*************:8001
2025-06-09 18:59:58,037 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-09 18:59:58,039 - werkzeug - INFO -  * Restarting with stat
/Users/<USER>/Documents/augment-projects/Agentic Social Handler/airline_phi3_env/lib/python3.9/site-packages/urllib3/__init__.py:35: NotOpenSSLWarning: urllib3 v2 only supports OpenSSL 1.1.1+, currently the 'ssl' module is compiled with 'LibreSSL 2.8.3'. See: https://github.com/urllib3/urllib3/issues/3020
  warnings.warn(
2025-06-09 18:59:59,951 - phi3_airline_service - INFO - 🎯 Initializing Phi-3 Mini for airline customer service
2025-06-09 18:59:59,951 - phi3_airline_service - INFO - 📱 Device: cpu
2025-06-09 18:59:59,951 - phi3_airline_service - INFO - 💾 Cache directory: ./models
2025-06-09 18:59:59,981 - werkzeug - WARNING -  * Debugger is active!
2025-06-09 18:59:59,997 - werkzeug - INFO -  * Debugger PIN: 569-************-06-09 19:00:00,028 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:00] "GET /health HTTP/1.1" 200 -
2025-06-09 19:00:01,080 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:01] "GET /health HTTP/1.1" 200 -
2025-06-09 19:00:01,091 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:01] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:01,116 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:01] "GET /api/v1/social-accounts/ HTTP/1.1" 200 -
2025-06-09 19:00:18,169 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:18] "OPTIONS /api/v1/auth/login HTTP/1.1" 200 -
2025-06-09 19:00:18,455 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:18] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-09 19:00:20,156 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:20] "OPTIONS /api/v1/auth/me HTTP/1.1" 200 -
2025-06-09 19:00:20,164 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:20] "GET /api/v1/auth/me HTTP/1.1" 200 -
2025-06-09 19:00:20,227 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:20] "GET /api/v1/monitoring/status HTTP/1.1" 200 -
2025-06-09 19:00:20,227 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:20] "GET /api/v1/social-accounts/ HTTP/1.1" 200 -
2025-06-09 19:00:20,228 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:20] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:20,238 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:20] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:21,647 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:21] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:21,683 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:21] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:22,216 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:22] "GET /api/v1/social-accounts/ HTTP/1.1" 200 -
2025-06-09 19:00:22,227 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:22] "GET /health HTTP/1.1" 200 -
2025-06-09 19:00:23,962 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:23] "GET /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 19:00:31,107 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:31] "OPTIONS /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 19:00:31,112 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:31] "PUT /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 19:00:31,116 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:31] "GET /api/v1/social-accounts/ HTTP/1.1" 200 -
2025-06-09 19:00:34,406 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:34] "OPTIONS /api/v1/monitoring/toggle HTTP/1.1" 200 -
2025-06-09 19:00:34,413 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:34] "POST /api/v1/monitoring/toggle HTTP/1.1" 200 -
2025-06-09 19:00:34,981 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:34] "POST /api/v1/monitoring/toggle HTTP/1.1" 200 -
2025-06-09 19:00:36,254 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:36] "GET /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 19:00:37,498 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:37] "OPTIONS /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 19:00:37,503 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:37] "PUT /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 19:00:37,507 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:37] "GET /api/v1/social-accounts/ HTTP/1.1" 200 -
2025-06-09 19:00:40,374 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:40] "OPTIONS /api/v1/monitoring/trigger HTTP/1.1" 200 -
2025-06-09 19:00:40,403 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:40] "GET /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 19:00:40,404 - app.services.twitter_query_builder.TwitterQueryBuilder - INFO - Built Twitter query: ((@KarthikHar25823) #KarthikHar25823) -is:retweet
2025-06-09 19:00:40,836 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:40] "GET /api/v1/dashboard/stats HTTP/1.1" 200 -
2025-06-09 19:00:40,837 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:40] "POST /api/v1/monitoring/trigger HTTP/1.1" 200 -
2025-06-09 19:00:40,852 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:40] "GET /api/v1/monitoring/status HTTP/1.1" 200 -
2025-06-09 19:00:50,441 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:50] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:50,460 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:50] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:53,304 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:53] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:53,323 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:53] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:53,948 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:53] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:53,960 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:53] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:54,479 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:54] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:54,493 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:54] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:54,897 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:54] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:54,927 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:54] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:55,065 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:55] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:55,084 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:55] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:55,258 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:55] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:55,268 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:55] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:55,482 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:55] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:55,492 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:55] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:55,687 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:55] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:55,699 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:55] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:55,877 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:55] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:55,889 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:55] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:56,054 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:56] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:56,075 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:56] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:57,849 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:57] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:57,854 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:57] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:58,641 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:58] "GET /api/v1/social-accounts/ HTTP/1.1" 200 -
2025-06-09 19:00:59,403 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:59] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:59,414 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:59] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:00:59,914 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:00:59] "GET /api/v1/social-accounts/ HTTP/1.1" 200 -
2025-06-09 19:01:06,262 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:01:06] "OPTIONS /api/v1/monitoring/toggle HTTP/1.1" 200 -
2025-06-09 19:01:06,266 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:01:06] "POST /api/v1/monitoring/toggle HTTP/1.1" 200 -
2025-06-09 19:01:06,655 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:01:06] "POST /api/v1/monitoring/toggle HTTP/1.1" 200 -
2025-06-09 19:01:08,009 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:01:08] "GET /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 19:01:08,998 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:01:08] "OPTIONS /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 19:01:09,001 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:01:09] "PUT /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 19:01:09,005 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:01:09] "GET /api/v1/social-accounts/ HTTP/1.1" 200 -
2025-06-09 19:01:10,231 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:01:10] "GET /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 19:01:11,370 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:01:11] "PUT /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 19:01:11,374 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:01:11] "GET /api/v1/social-accounts/ HTTP/1.1" 200 -
2025-06-09 19:01:29,271 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:01:29] "GET /api/v1/analytics/sentiment HTTP/1.1" 200 -
2025-06-09 19:01:29,271 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:01:29] "GET /api/v1/analytics/timeline HTTP/1.1" 200 -
2025-06-09 19:01:29,272 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:01:29] "GET /api/v1/analytics/platform HTTP/1.1" 200 -
2025-06-09 19:01:33,837 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:01:33] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:01:33,851 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:01:33] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:01:35,180 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:01:35] "GET /api/v1/social-accounts/ HTTP/1.1" 200 -
2025-06-09 19:01:36,022 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:01:36] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:01:36,033 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:01:36] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:01:37,514 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:01:37] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:01:37,523 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:01:37] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:01:37,687 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:01:37] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:01:37,697 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:01:37] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:01:37,846 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:01:37] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:01:37,851 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:01:37] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:01:38,038 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:01:38] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:01:38,046 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:01:38] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:01:38,850 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:01:38] "GET /api/v1/social-accounts/ HTTP/1.1" 200 -
2025-06-09 19:01:57,944 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:01:57] "GET /api/v1/social-accounts/ HTTP/1.1" 200 -
2025-06-09 19:02:20,262 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:02:20] "GET /health HTTP/1.1" 200 -
2025-06-09 19:03:46,081 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:03:46] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:03:46,120 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:03:46] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:03:47,888 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:03:47] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:03:47,898 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:03:47] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:03:48,136 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:03:48] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:03:48,146 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:03:48] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:03:48,470 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:03:48] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:03:48,477 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:03:48] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:03:48,712 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:03:48] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:03:48,721 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:03:48] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:03:48,918 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:03:48] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:03:48,923 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:03:48] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:03:49,145 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:03:49] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:03:49,158 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:03:49] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:03:49,313 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:03:49] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:03:49,321 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:03:49] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:03:50,512 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:03:50] "GET /api/v1/social-accounts/ HTTP/1.1" 200 -
2025-06-09 19:03:56,610 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:03:56] "GET /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 19:04:00,266 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:04:00] "OPTIONS /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 19:04:00,271 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:04:00] "PUT /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 19:04:00,288 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:04:00] "GET /api/v1/social-accounts/ HTTP/1.1" 200 -
2025-06-09 19:04:20,245 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:04:20] "GET /health HTTP/1.1" 200 -
2025-06-09 19:06:03,402 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:06:03] "OPTIONS /api/v1/monitoring/trigger HTTP/1.1" 200 -
2025-06-09 19:06:03,428 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:06:03] "GET /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 19:06:03,429 - app.services.twitter_query_builder.TwitterQueryBuilder - INFO - Built Twitter query: ((@KarthikHar25823) #KarthikHar25823) -is:retweet
2025-06-09 19:06:20,247 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:06:20] "GET /health HTTP/1.1" 200 -
2025-06-09 19:06:55,710 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:06:55] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:06:55,741 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:06:55] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:06:57,675 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:06:57] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:06:57,689 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:06:57] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:06:58,727 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:06:58] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:06:58,736 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:06:58] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:06:59,159 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:06:59] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:06:59,169 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:06:59] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:06:59,468 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:06:59] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:06:59,480 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:06:59] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:06:59,732 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:06:59] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:06:59,747 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:06:59] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:06:59,961 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:06:59] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:06:59,971 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:06:59] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:07:00,159 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:07:00] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:07:00,172 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:07:00] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:07:00,380 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:07:00] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:07:00,389 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:07:00] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:07:00,591 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:07:00] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:07:00,600 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:07:00] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:07:00,772 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:07:00] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:07:00,790 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:07:00] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:07:00,951 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:07:00] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:07:00,975 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:07:00] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 19:08:00,314 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:08:00] "GET /api/v1/social-accounts/ HTTP/1.1" 200 -
2025-06-09 19:08:01,303 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:08:01] "GET /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 19:08:05,011 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:08:05] "OPTIONS /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 19:08:05,015 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:08:05] "PUT /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 19:08:05,019 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:08:05] "GET /api/v1/social-accounts/ HTTP/1.1" 200 -
2025-06-09 19:08:20,242 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:08:20] "GET /health HTTP/1.1" 200 -
2025-06-09 19:10:20,568 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:10:20] "GET /health HTTP/1.1" 200 -
