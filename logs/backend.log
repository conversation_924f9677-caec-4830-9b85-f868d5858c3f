/Users/<USER>/Documents/augment-projects/Agentic Social Handler/airline_phi3_env/lib/python3.9/site-packages/urllib3/__init__.py:35: NotOpenSSLWarning: urllib3 v2 only supports OpenSSL 1.1.1+, currently the 'ssl' module is compiled with 'LibreSSL 2.8.3'. See: https://github.com/urllib3/urllib3/issues/3020
  warnings.warn(
2025-06-09 20:15:21,499 - phi3_airline_service - INFO - 🎯 Initializing Phi-3 Mini for airline customer service
2025-06-09 20:15:21,515 - phi3_airline_service - INFO - 📱 Device: cpu
2025-06-09 20:15:21,515 - phi3_airline_service - INFO - 💾 Cache directory: ./models
✅ Phi-3 Mini service available
✅ User management available
✅ Secure credential management available
✅ Twitter client ready (using secure credentials)
✅ Phi-3 Mini service ready (will initialize on first use)
🚀 Starting Agentic ORM Backend...
✅ Database initialized
✅ Twitter client ready
✅ User management enabled
👤 Default admin: username=admin, password=admin123
📱 Admin UI will show real data
🌐 Backend running on http://localhost:8001
📚 API Endpoints:
   - GET  /health
   - GET  /api/v1/social-accounts/
   - GET  /api/v1/responses/
   - POST /api/v1/responses/{id}/approve
   - POST /api/v1/responses/{id}/regenerate
   - POST /api/v1/auth/login
   - POST /api/v1/auth/logout
   - GET  /api/v1/auth/me
   - GET  /api/v1/users/
   - POST /api/v1/users/
   - PUT  /api/v1/users/{id}/social-accounts
   - DELETE /api/v1/users/{id}
   - GET  /api/v1/credentials/
   - POST /api/v1/credentials/
   - DELETE /api/v1/credentials/{id}

 * Serving Flask app 'real_time_backend'
 * Debug mode: on
2025-06-09 20:15:21,564 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8001
 * Running on http://*************:8001
2025-06-09 20:15:21,564 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-09 20:15:21,565 - werkzeug - INFO -  * Restarting with stat
/Users/<USER>/Documents/augment-projects/Agentic Social Handler/airline_phi3_env/lib/python3.9/site-packages/urllib3/__init__.py:35: NotOpenSSLWarning: urllib3 v2 only supports OpenSSL 1.1.1+, currently the 'ssl' module is compiled with 'LibreSSL 2.8.3'. See: https://github.com/urllib3/urllib3/issues/3020
  warnings.warn(
2025-06-09 20:15:23,349 - phi3_airline_service - INFO - 🎯 Initializing Phi-3 Mini for airline customer service
2025-06-09 20:15:23,349 - phi3_airline_service - INFO - 📱 Device: cpu
2025-06-09 20:15:23,349 - phi3_airline_service - INFO - 💾 Cache directory: ./models
2025-06-09 20:15:23,374 - werkzeug - WARNING -  * Debugger is active!
2025-06-09 20:15:23,383 - werkzeug - INFO -  * Debugger PIN: 569-************-06-09 20:15:23,406 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:15:23] "GET /health HTTP/1.1" 200 -
2025-06-09 20:15:24,442 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:15:24] "GET /health HTTP/1.1" 200 -
2025-06-09 20:15:24,452 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:15:24] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 20:15:24,476 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:15:24] "GET /api/v1/social-accounts/ HTTP/1.1" 200 -
2025-06-09 20:15:34,562 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:15:34] "GET /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 20:15:42,695 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:15:42] "OPTIONS /api/v1/auth/me HTTP/1.1" 200 -
2025-06-09 20:15:42,703 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:15:42] "GET /api/v1/auth/me HTTP/1.1" 200 -
2025-06-09 20:15:43,208 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:15:43] "GET /api/v1/auth/me HTTP/1.1" 200 -
2025-06-09 20:15:43,265 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:15:43] "GET /api/v1/monitoring/status HTTP/1.1" 200 -
2025-06-09 20:15:43,266 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:15:43] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 20:15:43,267 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:15:43] "GET /api/v1/social-accounts/ HTTP/1.1" 200 -
2025-06-09 20:15:43,270 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:15:43] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 20:15:45,279 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:15:45] "GET /health HTTP/1.1" 200 -
2025-06-09 20:15:49,926 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:15:49] "GET /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 20:16:30,756 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:16:30] "PUT /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 20:16:42,318 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:16:42] "GET /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 20:16:54,732 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:16:54] "GET /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 20:16:54,733 - app.services.twitter_query_builder.TwitterQueryBuilder - INFO - Built Twitter query: ((@KarthikHar25823) #KarthikHar25823) -is:retweet
2025-06-09 20:17:43,280 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:17:43] "GET /health HTTP/1.1" 200 -
/Applications/Xcode.app/Contents/Developer/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/resource_tracker.py:216: UserWarning: resource_tracker: There appear to be 1 leaked semaphore objects to clean up at shutdown
  warnings.warn('resource_tracker: There appear to be %d '
/Applications/Xcode.app/Contents/Developer/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/resource_tracker.py:216: UserWarning: resource_tracker: There appear to be 1 leaked semaphore objects to clean up at shutdown
  warnings.warn('resource_tracker: There appear to be %d '
