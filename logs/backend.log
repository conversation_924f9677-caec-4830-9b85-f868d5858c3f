/Users/<USER>/Documents/augment-projects/Agentic Social Handler/airline_phi3_env/lib/python3.9/site-packages/urllib3/__init__.py:35: NotOpenSSLWarning: urllib3 v2 only supports OpenSSL 1.1.1+, currently the 'ssl' module is compiled with 'LibreSSL 2.8.3'. See: https://github.com/urllib3/urllib3/issues/3020
  warnings.warn(
2025-06-09 20:10:53,762 - phi3_airline_service - INFO - 🎯 Initializing Phi-3 Mini for airline customer service
2025-06-09 20:10:53,787 - phi3_airline_service - INFO - 📱 Device: cpu
2025-06-09 20:10:53,787 - phi3_airline_service - INFO - 💾 Cache directory: ./models
✅ Phi-3 Mini service available
✅ User management available
✅ Secure credential management available
✅ Twitter client ready (using secure credentials)
✅ Phi-3 Mini service ready (will initialize on first use)
🚀 Starting Agentic ORM Backend...
✅ Database initialized
✅ Twitter client ready
✅ User management enabled
👤 Default admin: username=admin, password=admin123
📱 Admin UI will show real data
🌐 Backend running on http://localhost:8001
📚 API Endpoints:
   - GET  /health
   - GET  /api/v1/social-accounts/
   - GET  /api/v1/responses/
   - POST /api/v1/responses/{id}/approve
   - POST /api/v1/responses/{id}/regenerate
   - POST /api/v1/auth/login
   - POST /api/v1/auth/logout
   - GET  /api/v1/auth/me
   - GET  /api/v1/users/
   - POST /api/v1/users/
   - PUT  /api/v1/users/{id}/social-accounts
   - DELETE /api/v1/users/{id}
   - GET  /api/v1/credentials/
   - POST /api/v1/credentials/
   - DELETE /api/v1/credentials/{id}

 * Serving Flask app 'real_time_backend'
 * Debug mode: on
2025-06-09 20:10:53,833 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8001
 * Running on http://*************:8001
2025-06-09 20:10:53,833 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-09 20:10:53,834 - werkzeug - INFO -  * Restarting with stat
/Users/<USER>/Documents/augment-projects/Agentic Social Handler/airline_phi3_env/lib/python3.9/site-packages/urllib3/__init__.py:35: NotOpenSSLWarning: urllib3 v2 only supports OpenSSL 1.1.1+, currently the 'ssl' module is compiled with 'LibreSSL 2.8.3'. See: https://github.com/urllib3/urllib3/issues/3020
  warnings.warn(
2025-06-09 20:10:55,930 - phi3_airline_service - INFO - 🎯 Initializing Phi-3 Mini for airline customer service
2025-06-09 20:10:55,930 - phi3_airline_service - INFO - 📱 Device: cpu
2025-06-09 20:10:55,930 - phi3_airline_service - INFO - 💾 Cache directory: ./models
2025-06-09 20:10:55,961 - werkzeug - WARNING -  * Debugger is active!
2025-06-09 20:10:55,970 - werkzeug - INFO -  * Debugger PIN: 569-************-06-09 20:10:55,994 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:10:55] "GET /health HTTP/1.1" 200 -
2025-06-09 20:10:57,041 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:10:57] "GET /health HTTP/1.1" 200 -
2025-06-09 20:10:57,051 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:10:57] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 20:10:57,083 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:10:57] "GET /api/v1/social-accounts/ HTTP/1.1" 200 -
2025-06-09 20:12:22,134 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:22] "OPTIONS /api/v1/auth/login HTTP/1.1" 200 -
2025-06-09 20:12:22,406 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:22] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-09 20:12:24,235 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:24] "OPTIONS /api/v1/auth/me HTTP/1.1" 200 -
2025-06-09 20:12:24,241 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:24] "GET /api/v1/auth/me HTTP/1.1" 200 -
2025-06-09 20:12:24,328 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:24] "GET /api/v1/monitoring/status HTTP/1.1" 200 -
2025-06-09 20:12:24,328 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:24] "GET /api/v1/social-accounts/ HTTP/1.1" 200 -
2025-06-09 20:12:24,329 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:24] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 20:12:24,331 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:24] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 20:12:26,338 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:26] "GET /health HTTP/1.1" 200 -
2025-06-09 20:12:28,287 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:28] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 20:12:28,311 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:28] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 20:12:29,005 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:29] "GET /api/v1/social-accounts/ HTTP/1.1" 200 -
2025-06-09 20:12:30,872 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:30] "GET /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 20:12:34,285 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:34] "OPTIONS /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 20:12:34,295 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:34] "PUT /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 20:12:34,299 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:34] "GET /api/v1/social-accounts/ HTTP/1.1" 200 -
2025-06-09 20:12:39,230 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:39] "OPTIONS /api/v1/monitoring/trigger HTTP/1.1" 200 -
2025-06-09 20:12:39,252 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:39] "GET /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 20:12:39,253 - app.services.twitter_query_builder.TwitterQueryBuilder - INFO - Built Twitter query: ((@KarthikHar25823) #KarthikHar25823) -is:retweet
2025-06-09 20:12:39,917 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:39] "GET /api/v1/dashboard/stats HTTP/1.1" 200 -
2025-06-09 20:12:39,918 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:39] "POST /api/v1/monitoring/trigger HTTP/1.1" 200 -
2025-06-09 20:12:39,927 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:39] "GET /api/v1/monitoring/status HTTP/1.1" 200 -
2025-06-09 20:12:46,640 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:46] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 20:12:46,648 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:46] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 20:12:48,063 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:48] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 20:12:48,073 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:48] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 20:12:48,898 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:48] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 20:12:48,911 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:48] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 20:12:49,377 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:49] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 20:12:49,390 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:49] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 20:12:49,786 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:49] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 20:12:49,793 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:49] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 20:12:50,177 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:50] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 20:12:50,186 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:50] "GET /api/v1/responses/ HTTP/1.1" 200 -
2025-06-09 20:12:53,629 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:53] "GET /api/v1/social-accounts/ HTTP/1.1" 200 -
2025-06-09 20:12:54,617 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:54] "GET /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 20:12:58,563 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:58] "OPTIONS /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 20:12:58,567 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:58] "PUT /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 20:12:58,573 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:12:58] "GET /api/v1/social-accounts/ HTTP/1.1" 200 -
2025-06-09 20:13:00,174 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:13:00] "GET /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
2025-06-09 20:13:06,045 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 20:13:06] "GET /api/v1/social-accounts/KarthikHar25823/settings HTTP/1.1" 200 -
/Applications/Xcode.app/Contents/Developer/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/resource_tracker.py:216: UserWarning: resource_tracker: There appear to be 1 leaked semaphore objects to clean up at shutdown
  warnings.warn('resource_tracker: There appear to be %d '
