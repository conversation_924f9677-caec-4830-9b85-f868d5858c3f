#!/usr/bin/env python3
"""
Centralized Twitter Query Builder Service

This module provides a single, robust implementation for constructing Twitter search queries
across all parts of the application. It ensures consistency, proper validation, and 
eliminates code duplication.

Author: Agentic Social Handler
Version: 1.0.0
"""

import re
import urllib.parse
from typing import List, Dict, Optional, Any
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class TwitterQueryBuilder:
    """
    Centralized service for building Twitter search queries with proper validation,
    filtering, and error handling.
    """
    
    # Twitter API query limits and constraints
    MAX_QUERY_LENGTH = 1024  # Twitter's maximum query length
    MAX_KEYWORDS = 10        # Reasonable limit to prevent overly long queries
    MAX_HASHTAGS = 10        # Reasonable limit for hashtags
    
    def __init__(self):
        """Initialize the query builder with default settings"""
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def build_search_query(
        self,
        username: Optional[str] = None,
        keywords: Optional[List[str]] = None,
        hashtags: Optional[List[str]] = None,
        monitor_mentions: bool = True,
        monitor_from_user: bool = False,
        exclude_retweets: bool = True,
        language: Optional[str] = None,
        since_date: Optional[datetime] = None,
        until_date: Optional[datetime] = None,
        min_replies: Optional[int] = None,
        min_likes: Optional[int] = None,
        verified_only: bool = False
    ) -> str:
        """
        Build a comprehensive Twitter search query with all specified parameters.
        
        Args:
            username: Twitter username to monitor (without @)
            keywords: List of keywords to search for
            hashtags: List of hashtags to search for (with or without #)
            monitor_mentions: Include mentions of the username
            monitor_from_user: Include posts from the user
            exclude_retweets: Exclude retweets from results
            language: Language code (e.g., 'en', 'es', 'fr')
            since_date: Start date for search
            until_date: End date for search
            min_replies: Minimum number of replies
            min_likes: Minimum number of likes
            verified_only: Only include posts from verified accounts
            
        Returns:
            str: Properly formatted and validated Twitter search query
            
        Raises:
            ValueError: If the query parameters are invalid
            RuntimeError: If the resulting query exceeds Twitter's limits
        """
        query_parts = []
        
        try:
            # Add username-based monitoring
            if username:
                clean_username = self._clean_username(username)
                
                if monitor_mentions:
                    query_parts.append(f"@{clean_username}")
                
                if monitor_from_user:
                    query_parts.append(f"from:{clean_username}")
            
            # Add keyword monitoring
            if keywords:
                keyword_parts = self._build_keyword_query(keywords)
                query_parts.extend(keyword_parts)
            
            # Add hashtag monitoring
            if hashtags:
                hashtag_parts = self._build_hashtag_query(hashtags)
                query_parts.extend(hashtag_parts)
            
            # Validate we have at least one search criterion
            if not query_parts:
                raise ValueError("At least one search criterion must be provided")
            
            # Build base query with AND logic for airline relevance
            base_query = self._build_airline_focused_query(query_parts, username, monitor_mentions)
            
            # Add filters
            filters = []
            
            if exclude_retweets:
                filters.append("-is:retweet")
            
            if language:
                clean_lang = self._validate_language_code(language)
                filters.append(f"lang:{clean_lang}")
            
            if since_date:
                filters.append(f'since:{since_date.strftime("%Y-%m-%d")}')
            
            if until_date:
                filters.append(f'until:{until_date.strftime("%Y-%m-%d")}')
            
            if min_replies:
                filters.append(f"min_replies:{min_replies}")
            
            if min_likes:
                filters.append(f"min_faves:{min_likes}")
            
            if verified_only:
                filters.append("is:verified")
            
            # Combine base query with filters
            if filters:
                final_query = f"({base_query}) {' '.join(filters)}"
            else:
                final_query = base_query
            
            # Validate final query
            self._validate_query(final_query)
            
            self.logger.info(f"Built Twitter query: {final_query}")
            return final_query
            
        except Exception as e:
            self.logger.error(f"Error building Twitter query: {e}")
            raise
    
    def build_simple_query(
        self,
        account_settings: Dict[str, Any],
        username: str
    ) -> str:
        """
        Build a simple query from account settings (backward compatibility).
        
        Args:
            account_settings: Dictionary containing monitor settings
            username: Twitter username
            
        Returns:
            str: Twitter search query
        """
        return self.build_search_query(
            username=username,
            keywords=account_settings.get('monitor_keywords', []),
            hashtags=account_settings.get('monitor_hashtags', []),
            monitor_mentions=account_settings.get('monitor_mentions', True),
            exclude_retweets=True
        )
    
    def _clean_username(self, username: str) -> str:
        """Clean and validate Twitter username"""
        if not username:
            raise ValueError("Username cannot be empty")
        
        # Remove @ if present
        clean = username.strip().lstrip('@')
        
        # Validate username format
        if not re.match(r'^[A-Za-z0-9_]{1,15}$', clean):
            raise ValueError(f"Invalid Twitter username format: {username}")
        
        return clean
    
    def _build_keyword_query(self, keywords: List[str]) -> List[str]:
        """Build keyword query parts with proper filtering and validation"""
        if not keywords:
            return []
        
        # Filter and clean keywords
        clean_keywords = []
        for keyword in keywords[:self.MAX_KEYWORDS]:  # Limit number of keywords
            if keyword and str(keyword).strip():
                clean_keyword = str(keyword).strip()
                if clean_keyword and len(clean_keyword) <= 100:  # Reasonable keyword length limit
                    # Escape quotes in keywords
                    escaped_keyword = clean_keyword.replace('"', '\\"')
                    clean_keywords.append(f'"{escaped_keyword}"')
        
        if not clean_keywords:
            self.logger.warning("No valid keywords found after filtering")
            return []
        
        self.logger.debug(f"Built keyword query parts: {clean_keywords}")
        return clean_keywords
    
    def _build_hashtag_query(self, hashtags: List[str]) -> List[str]:
        """Build hashtag query parts with proper filtering and validation"""
        if not hashtags:
            return []
        
        # Filter and clean hashtags
        clean_hashtags = []
        for hashtag in hashtags[:self.MAX_HASHTAGS]:  # Limit number of hashtags
            if hashtag and str(hashtag).strip():
                clean_hashtag = str(hashtag).strip().lstrip('#')
                if clean_hashtag and len(clean_hashtag) <= 100:  # Reasonable hashtag length limit
                    # Validate hashtag format (alphanumeric and underscores)
                    if re.match(r'^[A-Za-z0-9_]+$', clean_hashtag):
                        clean_hashtags.append(f'#{clean_hashtag}')
                    else:
                        self.logger.warning(f"Invalid hashtag format: {hashtag}")
        
        if not clean_hashtags:
            self.logger.warning("No valid hashtags found after filtering")
            return []
        
        self.logger.debug(f"Built hashtag query parts: {clean_hashtags}")
        return clean_hashtags

    def _build_airline_focused_query(self, query_parts: List[str], username: str, monitor_mentions: bool) -> str:
        """
        Build airline-focused query prioritizing @mentions for better coverage.

        Logic:
        1. If @mentions enabled: @airline (primary) + optional keywords/hashtags
        2. If no @mentions: keywords OR hashtags (fallback)

        This ensures we capture all direct mentions while optionally including broader content.

        Args:
            query_parts: List of all query parts (mentions, keywords, hashtags)
            username: Airline username
            monitor_mentions: Whether mentions are enabled

        Returns:
            str: Properly structured query prioritizing @mentions
        """
        mention_parts = []
        content_parts = []

        # Separate mentions from content
        for part in query_parts:
            if part.startswith('@') or part.startswith('from:'):
                mention_parts.append(part)
            else:
                content_parts.append(part)

        # Prioritize @mentions for airline customer service
        if mention_parts and monitor_mentions:
            # Primary: @mentions only (most important for customer service)
            mention_query = " OR ".join(mention_parts)

            # Optional: Add keywords/hashtags with OR logic for broader coverage
            if content_parts:
                content_query = " OR ".join(content_parts)
                return f"({mention_query}) OR ({content_query})"
            else:
                # Just @mentions - this is the most reliable for customer service
                return mention_query

        elif content_parts:
            # Fallback: Only content without mentions (less reliable)
            self.logger.warning("Query contains only keywords/hashtags without airline mentions - may return irrelevant results")
            return " OR ".join(content_parts)

        else:
            raise ValueError("No valid query parts found")

    def _validate_language_code(self, language: str) -> str:
        """Validate and normalize language code"""
        if not language or not isinstance(language, str):
            raise ValueError("Language code must be a non-empty string")
        
        clean_lang = language.strip().lower()
        
        # Basic validation for common language codes
        if not re.match(r'^[a-z]{2}$', clean_lang):
            raise ValueError(f"Invalid language code format: {language}")
        
        return clean_lang
    
    def _validate_query(self, query: str) -> None:
        """Validate the final query against Twitter's constraints"""
        if not query or not query.strip():
            raise ValueError("Query cannot be empty")
        
        if len(query) > self.MAX_QUERY_LENGTH:
            raise RuntimeError(f"Query too long ({len(query)} chars, max {self.MAX_QUERY_LENGTH})")
        
        # Check for common problematic patterns
        if query.endswith(' OR "') or query.endswith(' OR ""'):
            raise ValueError("Query has malformed OR clause at the end")
        
        if ' OR ""' in query or '""' in query:
            raise ValueError("Query contains empty quotes")
        
        if query.startswith('"" OR'):
            raise ValueError("Query starts with empty OR clause")
        
        # Check for unmatched quotes
        quote_count = query.count('"')
        if quote_count % 2 != 0:
            raise ValueError("Query has unmatched quotes")
    
    def encode_query_for_url(self, query: str) -> str:
        """Safely encode query for URL parameters"""
        return urllib.parse.quote(query, safe='')
    
    def build_api_url(
        self,
        query: str,
        max_results: int = 10,
        tweet_fields: Optional[List[str]] = None,
        user_fields: Optional[List[str]] = None,
        expansions: Optional[List[str]] = None,
        since_id: Optional[str] = None,
        start_time: Optional[datetime] = None
    ) -> str:
        """
        Build complete Twitter API URL with query and parameters.
        
        Args:
            query: Twitter search query
            max_results: Maximum number of results (10-100)
            tweet_fields: List of tweet fields to include
            user_fields: List of user fields to include
            expansions: List of expansions to include
            since_id: Return results newer than this tweet ID
            start_time: Return results newer than this timestamp
            
        Returns:
            str: Complete Twitter API URL
        """
        # Validate and encode query
        self._validate_query(query)
        encoded_query = self.encode_query_for_url(query)
        
        # Build base URL
        base_url = "https://api.twitter.com/2/tweets/search/recent"
        
        # Build parameters
        params = {
            'query': encoded_query,
            'max_results': min(max(max_results, 10), 100)  # Clamp between 10-100
        }
        
        # Add optional parameters
        if tweet_fields:
            params['tweet.fields'] = ','.join(tweet_fields)
        
        if user_fields:
            params['user.fields'] = ','.join(user_fields)
        
        if expansions:
            params['expansions'] = ','.join(expansions)
        
        if since_id:
            params['since_id'] = since_id
        
        if start_time:
            params['start_time'] = start_time.strftime('%Y-%m-%dT%H:%M:%SZ')
        
        # Build final URL
        param_string = '&'.join([f"{k}={v}" for k, v in params.items()])
        final_url = f"{base_url}?{param_string}"
        
        self.logger.debug(f"Built Twitter API URL: {final_url}")
        return final_url

# Global instance for easy access
twitter_query_builder = TwitterQueryBuilder()

# Convenience functions for backward compatibility
def build_search_query(**kwargs) -> str:
    """Convenience function to build a search query"""
    return twitter_query_builder.build_search_query(**kwargs)

def build_simple_query(account_settings: Dict[str, Any], username: str) -> str:
    """Convenience function to build a simple query from settings"""
    return twitter_query_builder.build_simple_query(account_settings, username)

def build_api_url(query: str, **kwargs) -> str:
    """Convenience function to build a complete API URL"""
    return twitter_query_builder.build_api_url(query, **kwargs)
